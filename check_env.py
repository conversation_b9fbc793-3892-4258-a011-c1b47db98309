#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查环境和依赖的脚本
"""

import sys
import os

def check_python():
    """检查Python版本"""
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    return True

def check_dependencies():
    """检查依赖库"""
    dependencies = ['pandas', 'numpy']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} 已安装")
        except ImportError:
            print(f"✗ {dep} 未安装")
            return False
    return True

def check_xtquant():
    """检查XtQuant"""
    try:
        import xtdata
        print("✓ xtdata 模块导入成功")
        
        # 尝试获取一个简单的信息
        try:
            # 这个调用不需要连接MiniQMT
            print("尝试调用XtData函数...")
            result = xtdata.get_instrument_detail('000001.SZ')
            if result:
                print("✓ XtData函数调用成功")
                print(f"测试股票信息: {result.get('InstrumentName', '未知')}")
                return True
            else:
                print("✗ XtData函数返回空结果，可能需要启动MiniQMT")
                return False
        except Exception as e:
            print(f"✗ XtData函数调用失败: {e}")
            print("这通常意味着需要启动MiniQMT客户端")
            return False
            
    except ImportError as e:
        print(f"✗ 无法导入xtdata: {e}")
        print("请安装XtQuant: pip install xtquant")
        return False

def create_sample_data():
    """创建示例数据"""
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta
    
    print("\n创建示例数据...")
    
    # 生成示例数据
    dates = pd.date_range(start='2024-01-01', end='2024-01-10', freq='D')
    
    sample_data = []
    base_price = 50.0
    
    for i, date in enumerate(dates):
        # 模拟价格波动
        price_change = np.random.normal(0, 0.02)
        open_price = base_price * (1 + price_change)
        high_price = open_price * (1 + abs(np.random.normal(0, 0.01)))
        low_price = open_price * (1 - abs(np.random.normal(0, 0.01)))
        close_price = open_price + np.random.normal(0, 0.5)
        
        row = {
            '股票代码': '000651.SZ',
            '股票名称': '格力电器',
            '交易日期': date.strftime('%Y-%m-%d'),
            '开盘价': round(open_price, 2),
            '最高价': round(high_price, 2),
            '最低价': round(low_price, 2),
            '收盘价': round(close_price, 2),
            '前收盘价': round(base_price, 2),
            '成交量': np.random.randint(1000000, 10000000),
            '成交额': np.random.randint(50000000, 500000000),
            '流通市值': round(close_price * 6000000000 / 100000000, 2),  # 亿元
            '总市值': round(close_price * 6600000000 / 100000000, 2),    # 亿元
        }
        
        # 添加其他字段
        other_fields = [
            '净利润TTM', '现金流TTM', '净资产', '总资产', '总负债', '净利润(当季)',
            '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
            '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', 
            '中证2000成分股', '创业板指成分股', '新版申万一级行业名称', 
            '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:45收盘价', '09:55收盘价'
        ]
        
        for field in other_fields:
            if '成分股' in field:
                row[field] = np.random.choice([True, False])
            elif '行业名称' in field:
                row[field] = '家用电器'
            else:
                row[field] = np.random.uniform(1000000, 100000000)
        
        sample_data.append(row)
        base_price = close_price  # 下一天的基准价格
    
    df = pd.DataFrame(sample_data)
    
    # 保存示例数据
    filename = 'gree_sample_data.csv'
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    print(f"✓ 示例数据已保存到: {filename}")
    print(f"数据形状: {df.shape}")
    print("\n示例数据预览:")
    print(df[['股票代码', '股票名称', '交易日期', '开盘价', '收盘价', '成交量', '流通市值']].head())
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("环境检查工具")
    print("=" * 60)
    
    # 检查Python
    print("\n1. 检查Python环境:")
    check_python()
    
    # 检查依赖库
    print("\n2. 检查依赖库:")
    deps_ok = check_dependencies()
    
    # 检查XtQuant
    print("\n3. 检查XtQuant:")
    xt_ok = check_xtquant()
    
    # 创建示例数据
    print("\n4. 创建示例数据:")
    create_sample_data()
    
    # 总结
    print("\n" + "=" * 60)
    print("检查结果总结:")
    print(f"依赖库: {'✓ 正常' if deps_ok else '✗ 有问题'}")
    print(f"XtQuant: {'✓ 正常' if xt_ok else '✗ 需要启动MiniQMT'}")
    
    if not xt_ok:
        print("\n要获取真实数据，请:")
        print("1. 下载并安装MiniQMT客户端")
        print("2. 启动MiniQMT并登录")
        print("3. 运行 python test_gree_simple.py")
    else:
        print("\n环境正常，可以运行:")
        print("python test_gree_simple.py")
    
    print("\n示例数据文件: gree_sample_data.csv")

if __name__ == "__main__":
    main()
