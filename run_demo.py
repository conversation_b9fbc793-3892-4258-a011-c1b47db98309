#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示脚本 - 展示如何获取和处理股票数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def check_xtquant_available():
    """检查XtQuant是否可用"""
    try:
        import xtdata
        # 尝试简单调用
        result = xtdata.get_instrument_detail('000651.SZ')
        return result is not None
    except:
        return False

def generate_realistic_data(days=30):
    """生成更真实的格力电器数据"""
    print("生成格力电器模拟数据...")
    
    # 基于真实的格力电器数据特征
    base_info = {
        'stock_code': '000651.SZ',
        'stock_name': '格力电器',
        'base_price': 52.0,  # 基准价格
        'float_shares': 60.16,  # 流通股本(亿股)
        'total_shares': 66.17,  # 总股本(亿股)
        'industry_l1': '家用电器',
        'industry_l2': '白色家电',
        'industry_l3': '空调',
        'is_hs300': True,
        'is_sz50': False,
        'is_zz500': False,
        'is_zz1000': False,
        'is_zz2000': False,
        'is_cyb': False
    }
    
    # 生成日期序列（排除周末）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days*1.5)  # 多生成一些，然后筛选交易日
    
    all_dates = pd.date_range(start=start_date, end=end_date, freq='D')
    # 简单过滤：排除周六日
    trading_dates = [d for d in all_dates if d.weekday() < 5][-days:]
    
    data_list = []
    current_price = base_info['base_price']
    
    for i, date in enumerate(trading_dates):
        # 模拟价格波动
        daily_return = np.random.normal(0, 0.02)  # 日收益率，均值0，标准差2%
        
        # 计算当日价格
        prev_close = current_price
        open_price = prev_close * (1 + np.random.normal(0, 0.005))  # 开盘价相对前收盘的小幅波动
        
        # 当日波动范围
        intraday_volatility = abs(np.random.normal(0, 0.015))
        high_price = open_price * (1 + intraday_volatility)
        low_price = open_price * (1 - intraday_volatility)
        
        close_price = prev_close * (1 + daily_return)
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 成交量和成交额（基于真实特征）
        base_volume = np.random.uniform(20000000, 80000000)  # 2000万-8000万股
        volume = int(base_volume * (1 + abs(daily_return) * 5))  # 波动大时成交量增加
        amount = volume * (high_price + low_price) / 2  # 成交额
        
        # 计算市值
        float_market_cap = close_price * base_info['float_shares'] * 100000000 / 100000000  # 亿元
        total_market_cap = close_price * base_info['total_shares'] * 100000000 / 100000000  # 亿元
        
        # 模拟财务数据（季度数据，相对稳定）
        quarterly_net_profit = np.random.uniform(80, 120) * 100000000  # 8-12亿
        quarterly_cash_flow = np.random.uniform(60, 100) * 100000000   # 6-10亿
        net_assets = np.random.uniform(1500, 1800) * 100000000         # 1500-1800亿
        total_assets = np.random.uniform(2800, 3200) * 100000000       # 2800-3200亿
        total_liabilities = total_assets - net_assets
        
        # 模拟资金流向（亿元）
        total_amount_yi = amount / 100000000
        institution_ratio = np.random.uniform(0.15, 0.25)  # 机构占比15-25%
        large_ratio = np.random.uniform(0.20, 0.30)        # 大户占比20-30%
        medium_ratio = np.random.uniform(0.15, 0.25)       # 中户占比15-25%
        retail_ratio = 1 - institution_ratio - large_ratio - medium_ratio
        
        # 买卖比例（随机，但总体平衡）
        buy_sell_ratio = np.random.uniform(0.45, 0.55)
        
        # 特定时间点价格（模拟盘中价格）
        price_0935 = open_price + np.random.normal(0, 0.3)
        price_0945 = price_0935 + np.random.normal(0, 0.2)
        price_0955 = price_0945 + np.random.normal(0, 0.2)
        
        row_data = {
            '股票代码': base_info['stock_code'],
            '股票名称': base_info['stock_name'],
            '交易日期': date.strftime('%Y-%m-%d'),
            '开盘价': round(open_price, 2),
            '最高价': round(high_price, 2),
            '最低价': round(low_price, 2),
            '收盘价': round(close_price, 2),
            '前收盘价': round(prev_close, 2),
            '成交量': volume,
            '成交额': round(amount, 2),
            '流通市值': round(float_market_cap, 2),
            '总市值': round(total_market_cap, 2),
            '净利润TTM': round(quarterly_net_profit * 4, 2),  # TTM = 4个季度
            '现金流TTM': round(quarterly_cash_flow * 4, 2),
            '净资产': round(net_assets, 2),
            '总资产': round(total_assets, 2),
            '总负债': round(total_liabilities, 2),
            '净利润(当季)': round(quarterly_net_profit, 2),
            '中户资金买入额': round(total_amount_yi * medium_ratio * buy_sell_ratio, 2),
            '中户资金卖出额': round(total_amount_yi * medium_ratio * (1-buy_sell_ratio), 2),
            '大户资金买入额': round(total_amount_yi * large_ratio * buy_sell_ratio, 2),
            '大户资金卖出额': round(total_amount_yi * large_ratio * (1-buy_sell_ratio), 2),
            '散户资金买入额': round(total_amount_yi * retail_ratio * buy_sell_ratio, 2),
            '散户资金卖出额': round(total_amount_yi * retail_ratio * (1-buy_sell_ratio), 2),
            '机构资金买入额': round(total_amount_yi * institution_ratio * buy_sell_ratio, 2),
            '机构资金卖出额': round(total_amount_yi * institution_ratio * (1-buy_sell_ratio), 2),
            '沪深300成分股': base_info['is_hs300'],
            '上证50成分股': base_info['is_sz50'],
            '中证500成分股': base_info['is_zz500'],
            '中证1000成分股': base_info['is_zz1000'],
            '中证2000成分股': base_info['is_zz2000'],
            '创业板指成分股': base_info['is_cyb'],
            '新版申万一级行业名称': base_info['industry_l1'],
            '新版申万二级行业名称': base_info['industry_l2'],
            '新版申万三级行业名称': base_info['industry_l3'],
            '09:35收盘价': round(price_0935, 2),
            '09:45收盘价': round(price_0945, 2),
            '09:55收盘价': round(price_0955, 2)
        }
        
        data_list.append(row_data)
        current_price = close_price  # 更新当前价格
    
    return pd.DataFrame(data_list)

def analyze_data(df):
    """分析数据"""
    print("\n" + "="*60)
    print("数据分析报告")
    print("="*60)
    
    print(f"数据时间范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")
    print(f"总交易日数: {len(df)} 天")
    
    # 价格统计
    print(f"\n价格统计:")
    print(f"期间最高价: {df['最高价'].max():.2f} 元")
    print(f"期间最低价: {df['最低价'].min():.2f} 元")
    print(f"期末收盘价: {df['收盘价'].iloc[-1]:.2f} 元")
    print(f"期初收盘价: {df['收盘价'].iloc[0]:.2f} 元")
    
    # 计算收益率
    period_return = (df['收盘价'].iloc[-1] / df['收盘价'].iloc[0] - 1) * 100
    print(f"期间收益率: {period_return:.2f}%")
    
    # 成交统计
    print(f"\n成交统计:")
    print(f"日均成交量: {df['成交量'].mean()/10000:.0f} 万股")
    print(f"日均成交额: {df['成交额'].mean()/100000000:.2f} 亿元")
    
    # 市值统计
    print(f"\n市值统计:")
    print(f"当前流通市值: {df['流通市值'].iloc[-1]:.2f} 亿元")
    print(f"当前总市值: {df['总市值'].iloc[-1]:.2f} 亿元")
    
    # 财务数据
    print(f"\n财务数据 (最新):")
    print(f"净利润TTM: {df['净利润TTM'].iloc[-1]/100000000:.2f} 亿元")
    print(f"经营现金流TTM: {df['现金流TTM'].iloc[-1]/100000000:.2f} 亿元")
    print(f"净资产: {df['净资产'].iloc[-1]/100000000:.2f} 亿元")
    print(f"总资产: {df['总资产'].iloc[-1]/100000000:.2f} 亿元")
    
    # 指数成分股
    print(f"\n指数成分股:")
    index_fields = ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股']
    for field in index_fields:
        status = "是" if df[field].iloc[0] else "否"
        print(f"{field}: {status}")
    
    # 行业分类
    print(f"\n行业分类:")
    print(f"申万一级行业: {df['新版申万一级行业名称'].iloc[0]}")
    print(f"申万二级行业: {df['新版申万二级行业名称'].iloc[0]}")
    print(f"申万三级行业: {df['新版申万三级行业名称'].iloc[0]}")

def main():
    """主函数"""
    print("="*60)
    print("格力电器股票数据获取演示")
    print("="*60)
    
    # 检查XtQuant是否可用
    xtquant_available = check_xtquant_available()
    
    if xtquant_available:
        print("✓ 检测到XtQuant环境，可以获取真实数据")
        print("运行 'python test_gree_simple.py' 获取真实数据")
    else:
        print("✗ XtQuant环境不可用，将生成模拟数据进行演示")
    
    print("\n正在生成格力电器模拟数据...")
    
    # 生成数据
    df = generate_realistic_data(days=30)
    
    # 保存数据
    filename = 'gree_realistic_data.csv'
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✓ 数据已保存到: {filename}")
    
    # 分析数据
    analyze_data(df)
    
    # 显示前几行
    print(f"\n数据预览 (前5行):")
    display_cols = ['股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '成交量', '流通市值']
    print(df[display_cols].head().to_string(index=False))
    
    print(f"\n完整数据已保存到: {filename}")
    print("可以用Excel或其他工具打开查看完整数据")

if __name__ == "__main__":
    main()
