#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取格力电器股票数据的脚本
需要先启动MiniQMT客户端
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

try:
    import xtdata
    print("XtData模块导入成功")
except ImportError:
    print("错误：无法导入xtdata模块，请确保已安装XtQuant")
    exit(1)

def get_gree_stock_data(start_date='20240101', end_date='20241231'):
    """
    获取格力电器(000651.SZ)的股票数据

    Args:
        start_date: 开始日期，格式'YYYYMMDD'
        end_date: 结束日期，格式'YYYYMMDD'

    Returns:
        DataFrame: 包含所需字段的数据
    """

    # 格力电器股票代码
    stock_code = '000651.SZ'
    stock_name = '格力电器'

    print(f"开始获取{stock_name}({stock_code})的数据...")
    print(f"时间范围: {start_date} 到 {end_date}")

    try:
        # 1. 先下载历史数据确保本地有数据
        print("正在下载历史K线数据...")
        xtdata.download_history_data(stock_code, period='1d',
                                   start_time=start_date, end_time=end_date)

        # 2. 获取基本行情数据
        print("获取基本行情数据...")
        market_data = xtdata.get_market_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1d',
            start_time=start_date,
            end_time=end_date,
            dividend_type='none'  # 不复权
        )

        # 3. 获取分钟级数据用于计算特定时间点价格
        print("获取分钟级数据...")
        minute_data = None
        try:
            xtdata.download_history_data(stock_code, period='1m',
                                       start_time=start_date, end_time=end_date)
            minute_data = xtdata.get_market_data(
                field_list=['close'],
                stock_list=[stock_code],
                period='1m',
                start_time=start_date,
                end_time=end_date,
                dividend_type='none'
            )
        except Exception as e:
            print(f"分钟数据获取失败: {e}")

        # 4. 获取财务数据
        print("获取财务数据...")
        financial_data = None
        try:
            # 下载财务数据
            xtdata.download_financial_data([stock_code],
                                         ['Balance', 'Income', 'CashFlow', 'Pershareindex'])

            # 获取财务数据
            financial_data = xtdata.get_financial_data(
                stock_list=[stock_code],
                table_list=['Balance', 'Income', 'CashFlow', 'Pershareindex'],
                start_time=start_date,
                end_time=end_date
            )
        except Exception as e:
            print(f"财务数据获取失败: {e}")

        # 5. 获取合约基础信息
        print("获取合约基础信息...")
        instrument_info = xtdata.get_instrument_detail(stock_code, iscomplete=True)

        # 6. 获取板块信息
        print("获取板块信息...")
        sector_info = {}
        try:
            xtdata.download_sector_data()
            sector_list = xtdata.get_sector_list()

            # 检查是否属于主要指数
            index_sectors = {
                '沪深300成分股': [s for s in sector_list if '沪深300' in s],
                '上证50成分股': [s for s in sector_list if '上证50' in s],
                '中证500成分股': [s for s in sector_list if '中证500' in s],
                '中证1000成分股': [s for s in sector_list if '中证1000' in s],
                '中证2000成分股': [s for s in sector_list if '中证2000' in s],
                '创业板指成分股': [s for s in sector_list if '创业板' in s]
            }

            for index_name, sectors in index_sectors.items():
                sector_info[index_name] = False
                for sector in sectors:
                    try:
                        stocks_in_sector = xtdata.get_stock_list_in_sector(sector)
                        if stock_code in stocks_in_sector:
                            sector_info[index_name] = True
                            break
                    except:
                        continue

        except Exception as e:
            print(f"板块信息获取失败: {e}")
            for key in ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股']:
                sector_info[key] = False
        
        # 7. 处理数据
        if not market_data:
            print("未获取到行情数据")
            return None

        # 构建DataFrame
        result_data = []

        # 获取时间序列
        if 'close' in market_data and stock_code in market_data['close'].index:
            dates = market_data['close'].columns

            for date in dates:
                # 基础行情数据
                row_data = {
                    '股票代码': stock_code,
                    '股票名称': stock_name,
                    '交易日期': pd.to_datetime(str(date)).strftime('%Y-%m-%d'),
                    '开盘价': market_data.get('open', {}).get(stock_code, {}).get(date, np.nan) if 'open' in market_data else np.nan,
                    '最高价': market_data.get('high', {}).get(stock_code, {}).get(date, np.nan) if 'high' in market_data else np.nan,
                    '最低价': market_data.get('low', {}).get(stock_code, {}).get(date, np.nan) if 'low' in market_data else np.nan,
                    '收盘价': market_data.get('close', {}).get(stock_code, {}).get(date, np.nan) if 'close' in market_data else np.nan,
                    '前收盘价': market_data.get('preClose', {}).get(stock_code, {}).get(date, np.nan) if 'preClose' in market_data else np.nan,
                    '成交量': market_data.get('volume', {}).get(stock_code, {}).get(date, np.nan) if 'volume' in market_data else np.nan,
                    '成交额': market_data.get('amount', {}).get(stock_code, {}).get(date, np.nan) if 'amount' in market_data else np.nan,
                }

                # 计算市值（如果有股本信息）
                close_price = row_data['收盘价']
                if instrument_info and not pd.isna(close_price):
                    float_volume = instrument_info.get('FloatVolume', np.nan)
                    total_volume = instrument_info.get('TotalVolume', np.nan)

                    if not pd.isna(float_volume):
                        row_data['流通市值'] = close_price * float_volume
                    else:
                        row_data['流通市值'] = np.nan

                    if not pd.isna(total_volume):
                        row_data['总市值'] = close_price * total_volume
                    else:
                        row_data['总市值'] = np.nan
                else:
                    row_data['流通市值'] = np.nan
                    row_data['总市值'] = np.nan

                # 财务数据（需要匹配最近的财务报告）
                if financial_data and stock_code in financial_data:
                    stock_financial = financial_data[stock_code]

                    # 获取最新的财务数据
                    latest_balance = None
                    latest_income = None
                    latest_cashflow = None
                    latest_pershare = None

                    if 'Balance' in stock_financial and not stock_financial['Balance'].empty:
                        latest_balance = stock_financial['Balance'].iloc[-1]
                    if 'Income' in stock_financial and not stock_financial['Income'].empty:
                        latest_income = stock_financial['Income'].iloc[-1]
                    if 'CashFlow' in stock_financial and not stock_financial['CashFlow'].empty:
                        latest_cashflow = stock_financial['CashFlow'].iloc[-1]
                    if 'Pershareindex' in stock_financial and not stock_financial['Pershareindex'].empty:
                        latest_pershare = stock_financial['Pershareindex'].iloc[-1]

                    # 填充财务数据
                    row_data['净资产'] = latest_balance.get('total_equity', np.nan) if latest_balance is not None else np.nan
                    row_data['总资产'] = latest_balance.get('tot_assets', np.nan) if latest_balance is not None else np.nan
                    row_data['总负债'] = latest_balance.get('tot_liab', np.nan) if latest_balance is not None else np.nan
                    row_data['净利润TTM'] = latest_income.get('net_profit_incl_min_int_inc', np.nan) if latest_income is not None else np.nan
                    row_data['现金流TTM'] = latest_cashflow.get('net_cash_flows_oper_act', np.nan) if latest_cashflow is not None else np.nan
                    row_data['净利润(当季)'] = latest_income.get('net_profit_incl_min_int_inc', np.nan) if latest_income is not None else np.nan
                else:
                    row_data['净资产'] = np.nan
                    row_data['总资产'] = np.nan
                    row_data['总负债'] = np.nan
                    row_data['净利润TTM'] = np.nan
                    row_data['现金流TTM'] = np.nan
                    row_data['净利润(当季)'] = np.nan

                # 资金流向数据（XtQuant基础版本可能不支持，设为NaN）
                fund_flow_fields = [
                    '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
                    '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额'
                ]
                for field in fund_flow_fields:
                    row_data[field] = np.nan

                # 指数成分股信息
                for index_name in ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股']:
                    row_data[index_name] = sector_info.get(index_name, False)

                # 行业分类（申万行业，需要额外获取）
                row_data['新版申万一级行业名称'] = np.nan
                row_data['新版申万二级行业名称'] = np.nan
                row_data['新版申万三级行业名称'] = np.nan

                # 特定时间点价格（需要分钟数据计算）
                row_data['09:35收盘价'] = np.nan
                row_data['09:45收盘价'] = np.nan
                row_data['09:55收盘价'] = np.nan

                result_data.append(row_data)

        if not result_data:
            print("没有获取到有效数据")
            return None

        df = pd.DataFrame(result_data)
        print(f"成功获取{len(df)}条数据记录")
        return df
        
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        return None

def save_to_csv(df, filename='gree_stock_data.csv'):
    """
    保存数据到CSV文件
    """
    if df is not None:
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filename}")
        print(f"数据形状: {df.shape}")
        print("\n前5行数据预览:")
        print(df.head())
    else:
        print("没有数据可保存")

def main():
    """
    主函数
    """
    print("=" * 50)
    print("格力电器股票数据获取工具")
    print("=" * 50)
    
    # 检查连接
    try:
        # 尝试连接
        print("正在连接到MiniQMT...")
        # 可以先尝试获取一个简单的信息来测试连接
        test_info = xtdata.get_instrument_detail('000651.SZ')
        if test_info:
            print("连接成功!")
        else:
            print("警告: 无法获取合约信息，请检查MiniQMT是否正常运行")
    except Exception as e:
        print(f"连接失败: {e}")
        print("请确保:")
        print("1. MiniQMT客户端已启动")
        print("2. XtQuant库已正确安装")
        return
    
    # 设置时间范围（最近3个月作为测试）
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
    
    print(f"获取时间范围: {start_date} 到 {end_date}")
    
    # 获取数据
    df = get_gree_stock_data(start_date, end_date)
    
    # 保存数据
    save_to_csv(df)

if __name__ == "__main__":
    main()
