# 格力电器股票数据获取工具

本工具使用XtQuant API获取格力电器(000651.SZ)的详细股票数据，包括基本行情、财务数据、资金流向等信息。

## 环境要求

### 1. 软件环境
- Python 3.6+ 
- MiniQMT客户端
- XtQuant库

### 2. 安装步骤

1. **安装MiniQMT**
   - 从迅投官网下载并安装MiniQMT客户端
   - 启动MiniQMT并完成登录

2. **安装XtQuant库**
   ```bash
   pip install xtquant
   ```

3. **安装依赖库**
   ```bash
   pip install pandas numpy
   ```

## 使用方法

### 快速测试（推荐）

运行简化版脚本进行测试：

```bash
python test_gree_simple.py
```

这个脚本会：
- 测试XtData连接
- 获取最近30天的基本行情数据
- 计算流通市值和总市值
- 保存为CSV文件

### 完整版本

运行完整版脚本获取更多数据：

```bash
python get_gree_data.py
```

## 数据字段说明

### 基础行情数据
- **股票代码**: 000651.SZ
- **股票名称**: 格力电器
- **交易日期**: YYYY-MM-DD格式
- **开盘价**: 当日开盘价格
- **最高价**: 当日最高价格
- **最低价**: 当日最低价格
- **收盘价**: 当日收盘价格
- **前收盘价**: 前一交易日收盘价
- **成交量**: 当日成交股数
- **成交额**: 当日成交金额

### 市值数据
- **流通市值**: 收盘价 × 流通股本
- **总市值**: 收盘价 × 总股本

### 财务数据（需要财务数据权限）
- **净利润TTM**: 最近12个月净利润
- **现金流TTM**: 最近12个月经营现金流
- **净资产**: 股东权益合计
- **总资产**: 资产总计
- **总负债**: 负债合计
- **净利润(当季)**: 当季净利润

### 资金流向数据（需要Level2权限）
- **中户资金买入额/卖出额**: 中等资金规模交易
- **大户资金买入额/卖出额**: 大资金规模交易
- **散户资金买入额/卖出额**: 小资金规模交易
- **机构资金买入额/卖出额**: 机构投资者交易

### 指数成分股
- **沪深300成分股**: 是否为沪深300指数成分股
- **上证50成分股**: 是否为上证50指数成分股
- **中证500成分股**: 是否为中证500指数成分股
- **中证1000成分股**: 是否为中证1000指数成分股
- **中证2000成分股**: 是否为中证2000指数成分股
- **创业板指成分股**: 是否为创业板指数成分股

### 行业分类
- **新版申万一级行业名称**: 申万一级行业分类
- **新版申万二级行业名称**: 申万二级行业分类
- **新版申万三级行业名称**: 申万三级行业分类

### 特定时间点价格
- **09:35收盘价**: 9:35时刻的价格
- **09:45收盘价**: 9:45时刻的价格
- **09:55收盘价**: 9:55时刻的价格

## 输出文件

- **gree_stock_data_simple.csv**: 简化版数据文件
- **gree_stock_data.csv**: 完整版数据文件

## 注意事项

1. **MiniQMT必须运行**: 使用前必须启动MiniQMT客户端并登录
2. **数据权限**: 某些高级数据需要相应的数据权限
3. **网络连接**: 需要稳定的网络连接来下载数据
4. **数据延迟**: 实时数据可能有15分钟延迟
5. **使用限制**: 请遵守数据使用协议，不要过度频繁请求

## 故障排除

### 常见错误

1. **"无法导入xtdata模块"**
   - 检查XtQuant是否正确安装
   - 尝试重新安装: `pip install xtquant`

2. **"连接失败"**
   - 确保MiniQMT客户端已启动
   - 检查MiniQMT是否正常登录
   - 重启MiniQMT客户端

3. **"未获取到数据"**
   - 检查股票代码是否正确
   - 确认时间范围内有交易日
   - 检查网络连接

4. **"财务数据获取失败"**
   - 可能需要财务数据权限
   - 尝试缩小时间范围

## 扩展功能

### 获取多只股票数据

修改股票代码列表：
```python
stock_list = ['000651.SZ', '000858.SZ', '002415.SZ']  # 格力电器、五粮液、海康威视
```

### 自定义时间范围

```python
start_date = '20240101'  # 开始日期
end_date = '20241231'    # 结束日期
```

### 获取分钟级数据

```python
period = '1m'  # 1分钟
period = '5m'  # 5分钟
period = '1h'  # 1小时
```

## 联系方式

如有问题，请参考：
- XtQuant官方文档: https://dict.thinktrader.net/
- 迅投社区: https://www.xuntou.net/
- 客服QQ: 810315303
