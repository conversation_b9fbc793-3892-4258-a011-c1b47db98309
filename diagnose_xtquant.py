#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
XtQuant诊断脚本 - 详细检查XtQuant安装和配置问题
"""

import sys
import os
import subprocess

def check_python_info():
    """检查Python环境信息"""
    print("=" * 60)
    print("Python环境信息")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"Python位数: {sys.maxsize > 2**32 and '64位' or '32位'}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径列表:")
    for path in sys.path:
        print(f"  {path}")

def check_installed_packages():
    """检查已安装的包"""
    print("\n" + "=" * 60)
    print("已安装的相关包")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            xt_packages = [line for line in lines if 'xt' in line.lower()]
            if xt_packages:
                print("找到的XtQuant相关包:")
                for pkg in xt_packages:
                    print(f"  {pkg}")
            else:
                print("未找到XtQuant相关包")
                
            # 检查其他相关包
            relevant_packages = ['pandas', 'numpy']
            print("\n其他相关包:")
            for pkg in relevant_packages:
                found = [line for line in lines if line.lower().startswith(pkg.lower())]
                if found:
                    print(f"  ✓ {found[0]}")
                else:
                    print(f"  ✗ {pkg} 未安装")
        else:
            print(f"无法获取包列表: {result.stderr}")
    except Exception as e:
        print(f"检查包列表时出错: {e}")

def try_import_xtquant():
    """尝试导入XtQuant的各种方式"""
    print("\n" + "=" * 60)
    print("尝试导入XtQuant")
    print("=" * 60)
    
    # 方式1: 直接导入xtdata
    print("1. 尝试导入 xtdata:")
    try:
        import xtdata
        print("  ✓ xtdata 导入成功")
        print(f"  模块路径: {xtdata.__file__ if hasattr(xtdata, '__file__') else '未知'}")
        return True
    except ImportError as e:
        print(f"  ✗ xtdata 导入失败: {e}")
    except Exception as e:
        print(f"  ✗ xtdata 导入异常: {e}")
    
    # 方式2: 导入xtquant
    print("\n2. 尝试导入 xtquant:")
    try:
        import xtquant
        print("  ✓ xtquant 导入成功")
        print(f"  模块路径: {xtquant.__file__ if hasattr(xtquant, '__file__') else '未知'}")
        
        # 尝试从xtquant导入xtdata
        try:
            from xtquant import xtdata
            print("  ✓ 从 xtquant 导入 xtdata 成功")
            return True
        except Exception as e:
            print(f"  ✗ 从 xtquant 导入 xtdata 失败: {e}")
            
    except ImportError as e:
        print(f"  ✗ xtquant 导入失败: {e}")
    except Exception as e:
        print(f"  ✗ xtquant 导入异常: {e}")
    
    # 方式3: 检查可能的安装位置
    print("\n3. 检查可能的安装位置:")
    possible_paths = [
        os.path.join(os.path.dirname(sys.executable), 'Lib', 'site-packages'),
        os.path.join(os.path.dirname(sys.executable), 'lib', 'python3.8', 'site-packages'),
        os.path.expanduser('~/.local/lib/python3.8/site-packages'),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"  检查路径: {path}")
            xt_dirs = [d for d in os.listdir(path) if 'xt' in d.lower()]
            if xt_dirs:
                print(f"    找到相关目录: {xt_dirs}")
            else:
                print(f"    未找到xt相关目录")
    
    return False

def check_xtquant_installation():
    """检查XtQuant安装"""
    print("\n" + "=" * 60)
    print("XtQuant安装检查")
    print("=" * 60)
    
    # 检查pip安装
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'show', 'xtquant'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ pip show xtquant 成功:")
            print(result.stdout)
        else:
            print("✗ pip show xtquant 失败:")
            print(result.stderr)
            
            # 尝试安装
            print("\n尝试重新安装 xtquant...")
            install_result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'xtquant', '--upgrade'], 
                                          capture_output=True, text=True)
            if install_result.returncode == 0:
                print("✓ xtquant 安装成功")
                print(install_result.stdout)
            else:
                print("✗ xtquant 安装失败")
                print(install_result.stderr)
                
    except Exception as e:
        print(f"检查安装时出错: {e}")

def check_system_requirements():
    """检查系统要求"""
    print("\n" + "=" * 60)
    print("系统要求检查")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major == 3 and 6 <= python_version.minor <= 12:
        print("✓ Python版本符合要求 (3.6-3.12)")
    else:
        print("✗ Python版本可能不兼容，建议使用Python 3.6-3.12")
    
    # 检查系统位数
    import platform
    print(f"系统架构: {platform.architecture()}")
    print(f"系统平台: {platform.platform()}")
    
    if '64bit' in platform.architecture()[0]:
        print("✓ 64位系统")
    else:
        print("⚠️ 32位系统，可能存在兼容性问题")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案建议")
    print("=" * 60)
    
    print("如果XtQuant导入失败，请尝试以下解决方案：")
    print()
    print("1. 重新安装XtQuant:")
    print("   pip uninstall xtquant")
    print("   pip install xtquant")
    print()
    print("2. 使用清华源安装:")
    print("   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple xtquant")
    print()
    print("3. 检查Python环境:")
    print("   - 确保使用的是正确的Python环境")
    print("   - 如果使用虚拟环境，确保在正确的环境中安装")
    print()
    print("4. 手动下载安装:")
    print("   - 从迅投官网下载对应Python版本的whl文件")
    print("   - 使用 pip install 文件路径.whl 安装")
    print()
    print("5. 检查MiniQMT:")
    print("   - 确保MiniQMT客户端已启动")
    print("   - 确保MiniQMT已成功登录")
    print("   - 尝试重启MiniQMT")
    print()
    print("6. 权限问题:")
    print("   - 尝试以管理员身份运行命令提示符")
    print("   - 重新安装XtQuant")

def main():
    """主函数"""
    print("XtQuant 问题诊断工具")
    
    # 检查Python环境
    check_python_info()
    
    # 检查已安装的包
    check_installed_packages()
    
    # 检查系统要求
    check_system_requirements()
    
    # 检查XtQuant安装
    check_xtquant_installation()
    
    # 尝试导入
    import_success = try_import_xtquant()
    
    # 提供解决方案
    if not import_success:
        provide_solutions()
    else:
        print("\n" + "=" * 60)
        print("✓ XtQuant 导入成功！可以正常使用。")
        print("=" * 60)

if __name__ == "__main__":
    main()
