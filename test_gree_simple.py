#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版格力电器数据获取脚本
用于测试基本功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_xtdata_connection():
    """测试XtData连接"""
    try:
        import xtdata
        print("✓ XtData模块导入成功")
        
        # 测试连接
        test_info = xtdata.get_instrument_detail('000651.SZ')
        if test_info:
            print("✓ 连接MiniQMT成功")
            print(f"格力电器基本信息: {test_info.get('InstrumentName', '未知')}")
            return True
        else:
            print("✗ 无法获取合约信息")
            return False
            
    except ImportError:
        print("✗ 无法导入xtdata模块")
        print("请确保已安装XtQuant库")
        return False
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def get_basic_data():
    """获取基础数据"""
    try:
        import xtdata
        
        stock_code = '000651.SZ'
        
        # 设置时间范围（最近30天）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"获取时间范围: {start_date} 到 {end_date}")
        
        # 下载数据
        print("正在下载历史数据...")
        xtdata.download_history_data(stock_code, period='1d', 
                                   start_time=start_date, end_time=end_date)
        
        # 获取行情数据
        print("获取行情数据...")
        market_data = xtdata.get_market_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1d',
            start_time=start_date,
            end_time=end_date,
            dividend_type='none'
        )
        
        if not market_data or 'close' not in market_data:
            print("未获取到有效数据")
            return None
        
        # 获取合约信息
        instrument_info = xtdata.get_instrument_detail(stock_code, iscomplete=True)
        
        # 构建DataFrame
        result_data = []
        
        if stock_code in market_data['close'].index:
            dates = market_data['close'].columns
            
            for date in dates:
                row_data = {
                    '股票代码': stock_code,
                    '股票名称': '格力电器',
                    '交易日期': pd.to_datetime(str(date)).strftime('%Y-%m-%d'),
                    '开盘价': market_data['open'].loc[stock_code, date] if 'open' in market_data else np.nan,
                    '最高价': market_data['high'].loc[stock_code, date] if 'high' in market_data else np.nan,
                    '最低价': market_data['low'].loc[stock_code, date] if 'low' in market_data else np.nan,
                    '收盘价': market_data['close'].loc[stock_code, date] if 'close' in market_data else np.nan,
                    '前收盘价': market_data['preClose'].loc[stock_code, date] if 'preClose' in market_data else np.nan,
                    '成交量': market_data['volume'].loc[stock_code, date] if 'volume' in market_data else np.nan,
                    '成交额': market_data['amount'].loc[stock_code, date] if 'amount' in market_data else np.nan,
                }
                
                # 计算市值
                close_price = row_data['收盘价']
                if instrument_info and not pd.isna(close_price):
                    float_volume = instrument_info.get('FloatVolume', np.nan)
                    total_volume = instrument_info.get('TotalVolume', np.nan)
                    
                    if not pd.isna(float_volume):
                        row_data['流通市值'] = close_price * float_volume / 100000000  # 转换为亿元
                    else:
                        row_data['流通市值'] = np.nan
                        
                    if not pd.isna(total_volume):
                        row_data['总市值'] = close_price * total_volume / 100000000  # 转换为亿元
                    else:
                        row_data['总市值'] = np.nan
                else:
                    row_data['流通市值'] = np.nan
                    row_data['总市值'] = np.nan
                
                # 其他字段暂时设为NaN
                other_fields = [
                    '净利润TTM', '现金流TTM', '净资产', '总资产', '总负债', '净利润(当季)',
                    '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
                    '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
                    '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', 
                    '中证2000成分股', '创业板指成分股', '新版申万一级行业名称', 
                    '新版申万二级行业名称', '新版申万三级行业名称',
                    '09:35收盘价', '09:45收盘价', '09:55收盘价'
                ]
                
                for field in other_fields:
                    row_data[field] = np.nan
                
                result_data.append(row_data)
        
        if result_data:
            df = pd.DataFrame(result_data)
            print(f"成功获取{len(df)}条数据记录")
            return df
        else:
            print("没有获取到有效数据")
            return None
            
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        return None

def save_to_csv(df, filename='gree_stock_data_simple.csv'):
    """保存数据到CSV"""
    if df is not None:
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n数据已保存到: {filename}")
        print(f"数据形状: {df.shape}")
        print("\n数据预览:")
        print(df.head())
        
        # 显示数据统计
        print("\n数值字段统计:")
        numeric_cols = ['开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额', '流通市值', '总市值']
        for col in numeric_cols:
            if col in df.columns:
                print(f"{col}: 最小值={df[col].min():.2f}, 最大值={df[col].max():.2f}, 平均值={df[col].mean():.2f}")
    else:
        print("没有数据可保存")

def main():
    """主函数"""
    print("=" * 60)
    print("格力电器股票数据获取工具 (简化版)")
    print("=" * 60)
    
    # 检查连接
    if not test_xtdata_connection():
        print("\n请确保:")
        print("1. MiniQMT客户端已启动并登录")
        print("2. XtQuant库已正确安装")
        print("3. 网络连接正常")
        return
    
    print("\n开始获取数据...")
    
    # 获取数据
    df = get_basic_data()
    
    # 保存数据
    save_to_csv(df)
    
    print("\n" + "=" * 60)
    print("数据获取完成!")

if __name__ == "__main__":
    main()
