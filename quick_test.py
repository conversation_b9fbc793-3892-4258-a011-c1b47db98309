#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试XtQuant导入
"""

import sys
import os

print("Python路径:", sys.executable)
print("Python版本:", sys.version)

# 测试1: 检查pip list
print("\n=== 检查已安装的包 ===")
import subprocess
try:
    result = subprocess.run([sys.executable, '-m', 'pip', 'list'], capture_output=True, text=True)
    lines = result.stdout.split('\n')
    xt_packages = [line for line in lines if 'xt' in line.lower()]
    if xt_packages:
        print("找到XtQuant相关包:")
        for pkg in xt_packages:
            print(f"  {pkg}")
    else:
        print("未找到XtQuant包")
except Exception as e:
    print(f"检查包列表失败: {e}")

# 测试2: 尝试导入
print("\n=== 尝试导入 ===")
try:
    import xtdata
    print("✓ xtdata 导入成功")
    print(f"模块位置: {xtdata.__file__}")
except ImportError as e:
    print(f"✗ xtdata 导入失败: {e}")
    
    # 尝试其他导入方式
    try:
        import xtquant
        print("✓ xtquant 导入成功")
        from xtquant import xtdata
        print("✓ 从xtquant导入xtdata成功")
    except Exception as e2:
        print(f"✗ 其他导入方式也失败: {e2}")

# 测试3: 检查site-packages
print("\n=== 检查site-packages ===")
import site
site_packages = site.getsitepackages()
for sp in site_packages:
    print(f"site-packages路径: {sp}")
    if os.path.exists(sp):
        xt_dirs = [d for d in os.listdir(sp) if 'xt' in d.lower()]
        if xt_dirs:
            print(f"  找到xt相关目录: {xt_dirs}")
